import React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Filter } from "lucide-react";

interface TableColumnFilterProps {
  columnName: string;
  fieldKey: string;
  uniqueValues: string[];
  filterValue: string;
  selectedValue: string;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onFilterChange: (field: string, value: string) => void;
  onDropdownSelect: (field: string, value: string) => void;
  onClearFilter: (field: string) => void;
  placeholder?: string;
}

export function TableColumnFilter({
  columnName,
  fieldKey,
  uniqueValues,
  filterValue,
  selectedValue,
  isOpen,
  onOpenChange,
  onFilterChange,
  onDropdownSelect,
  onClearFilter,
  placeholder = "Type to search..."
}: TableColumnFilterProps) {
  return (
    <div className="flex items-center justify-between">
      <span>{columnName}</span>
      <Popover open={isOpen} onOpenChange={onOpenChange}>
        <PopoverTrigger asChild>
          <Button variant="ghost" size="icon" className="h-6 w-6">
            <Filter className={`h-3 w-3 ${filterValue || selectedValue ? 'text-primary' : 'text-gray-400'}`} />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-72 p-3" align="end">
          <div className="space-y-4">
            <h4 className="font-medium text-sm">Filter {columnName}</h4>

            {/* Dropdown filter */}
            <div className="space-y-1">
              <label className="text-xs font-medium">Select from dropdown</label>
              <Select
                value={selectedValue}
                onValueChange={(value) => onDropdownSelect(fieldKey, value)}
              >
                <SelectTrigger className="h-8 text-sm">
                  <SelectValue placeholder={`Select ${columnName.toLowerCase()}`} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All {columnName.toLowerCase()}s</SelectItem>
                  {uniqueValues.map((value) => (
                    <SelectItem key={value} value={value}>{value}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t"></span>
              </div>
              <div className="relative flex justify-center text-xs">
                <span className="bg-background px-2 text-muted-foreground">OR</span>
              </div>
            </div>

            {/* Text search filter */}
            <div className="space-y-1">
              <label className="text-xs font-medium">Search by text</label>
              <Input
                placeholder={placeholder}
                value={filterValue}
                onChange={(e) => onFilterChange(fieldKey, e.target.value)}
                className="h-8 text-sm"
              />
            </div>

            <div className="flex justify-between">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onClearFilter(fieldKey)}
                className="text-xs"
                disabled={!filterValue && !selectedValue}
              >
                Clear
              </Button>
              <Button
                size="sm"
                onClick={() => onOpenChange(false)}
                className="text-xs"
              >
                Apply
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
